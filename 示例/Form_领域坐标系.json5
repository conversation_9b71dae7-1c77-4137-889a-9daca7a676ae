{"id": "form_78ho4mxb7sp4m", "type": "form", "props": {"visible": true, "classNames": [], "events": [], "labelPosition": "vertical", "width": "100%", "size": "middle", "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "colon": false, "useDefaultGrid": false, "defaultGridSize": 2, "disabled": false, "datasourcePool": [{"title": "getCascaderData", "desc": "获取级联数据", "supportDesignerMode": true, "code": "module.exports=function(ctx, payload){ return [{\"children\":[{\"children\":\"\",\"disabled\":false,\"id\":3,\"label\":\"北京市\",\"parentId\":2,\"value\":\"110100\"}],\"disabled\":false,\"id\":2,\"label\":\"北京市\",\"parentId\":0,\"value\":\"110000\"},{\"children\":[{\"children\":\"\",\"disabled\":false,\"id\":2204,\"label\":\"海口市\",\"parentId\":2203,\"value\":\"460100\"},{\"children\":\"\",\"disabled\":false,\"id\":2209,\"label\":\"三亚市\",\"parentId\":2203,\"value\":\"460200\"},{\"children\":\"\",\"disabled\":false,\"id\":2214,\"label\":\"三沙市\",\"parentId\":2203,\"value\":\"460300\"},{\"children\":\"\",\"disabled\":false,\"id\":3228,\"label\":\"儋州市\",\"parentId\":2203,\"value\":\"83003\"}],\"disabled\":false,\"id\":2203,\"label\":\"海南省\",\"parentId\":0,\"value\":\"460000\"}] }"}, {"title": "getSelectTreeData", "desc": "获取下拉选择树数据", "supportDesignerMode": true, "code": "module.exports=function(ctx,payload){ return [{\"children\":[{\"children\":[{\"children\":\"\",\"disabled\":false,\"id\":1614,\"label\":\"操作系统全新安装\",\"parentId\":1569,\"value\":\"0042\"},{\"children\":\"\",\"disabled\":false,\"id\":1615,\"label\":\"软件问题导致操作系统无法启动\",\"parentId\":1569,\"value\":\"0043\"},{\"children\":\"\",\"disabled\":false,\"id\":1616,\"label\":\"操作系统数据备份、恢复\",\"parentId\":1569,\"value\":\"0044\"},{\"children\":\"\",\"disabled\":false,\"id\":1617,\"label\":\"操作系统软件安装补丁、版本升级\",\"parentId\":1569,\"value\":\"0045\"},{\"children\":\"\",\"disabled\":false,\"id\":1618,\"label\":\"操作系统常规维护（含磁盘、文件系统、用户、主机名、ip地址、路由、启动列表等增删改操作）\",\"parentId\":1569,\"value\":\"0046\"},{\"children\":\"\",\"disabled\":false,\"id\":1619,\"label\":\"操作系统非常规参数维护（含虚拟内存、进程管理、io管理等高级参数的分析调整）\",\"parentId\":1569,\"value\":\"0047\"},{\"children\":\"\",\"disabled\":false,\"id\":1620,\"label\":\"操作系统异常宕机分析处理（不含硬件故障导致，可能涉及crash分析）\",\"parentId\":1569,\"value\":\"0048\"},{\"children\":\"\",\"disabled\":false,\"id\":1621,\"label\":\"操作系统性能问题分析处理\",\"parentId\":1569,\"value\":\"0049\"}],\"disabled\":false,\"id\":1569,\"label\":\"操作系统\",\"parentId\":1554,\"value\":\"014\"},{\"children\":[{\"children\":\"\",\"disabled\":false,\"id\":1622,\"label\":\"双机环境搭建\",\"parentId\":1570,\"value\":\"0050\"},{\"children\":\"\",\"disabled\":false,\"id\":1623,\"label\":\"单机到双机环境改造\",\"parentId\":1570,\"value\":\"0051\"},{\"children\":\"\",\"disabled\":false,\"id\":1624,\"label\":\"双机到单机环境改造\",\"parentId\":1570,\"value\":\"0052\"},{\"children\":\"\",\"disabled\":false,\"id\":1625,\"label\":\"双机环境配置调整、测试\",\"parentId\":1570,\"value\":\"0053\"},{\"children\":\"\",\"disabled\":false,\"id\":1626,\"label\":\"双机环境切换失败等故障分析排查\",\"parentId\":1570,\"value\":\"0054\"}],\"disabled\":false,\"id\":1570,\"label\":\"高可用软件\",\"parentId\":1554,\"value\":\"015\"},{\"children\":[{\"children\":\"\",\"disabled\":false,\"id\":1627,\"label\":\"传统物理分区（lpar）规划，搭建，配置调整\",\"parentId\":1571,\"value\":\"0055\"},{\"children\":\"\",\"disabled\":false,\"id\":1628,\"label\":\"微分区（vios）规划，搭建，含微分区高可用搭建，配置调整\",\"parentId\":1571,\"value\":\"0056\"},{\"children\":\"\",\"disabled\":false,\"id\":1629,\"label\":\"微分区性能分析、故障排查（不涉及os层面）\",\"parentId\":1571,\"value\":\"0057\"}],\"disabled\":false,\"id\":1571,\"label\":\"系统分区\",\"parentId\":1554,\"value\":\"016\"}],\"disabled\":false,\"id\":1554,\"label\":\"软件调整\",\"parentId\":0,\"value\":\"02\"}] }"}, {"title": "getCostAuthorization", "desc": "获取腾讯云token", "supportDesignerMode": true, "code": "module.exports = async function(ctx, payload) {\n  const result = await ctx.http.get('http://flowweb.cn:8089/cos/getCosUploadSign')\n  return result\n}"}, {"title": "getInputAutoComplete", "desc": "获取输入框自动补全", "supportDesignerMode": false, "code": "module.exports = async function(ctx, payload) {\n  const treeData = await ctx.datasource.getSelectTreeData()\n  \n  return treeData[0].children.filter(item => item.label.includes(payload.value))\n}"}], "stylesheets": [{"title": "main", "id": "main", "desc": "主样式", "code": ""}], "utilsJs": [], "relations": []}, "children": [{"id": "input_rlx5n9bjm7ax6", "type": "input", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "input_rlx5n9bjm7ax6", "customDataBind": true, "label": "输入框", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "placeholder": "请输入", "defaultValue": "", "isAutoComplete": false, "options": [], "dataSourceFn": "", "isPassword": false}}, {"id": "textarea_dyky6fzakhx75", "type": "textarea", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "textarea_dyky6fzakhx75", "customDataBind": true, "label": "多行文本", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "placeholder": "请输入", "defaultValue": "", "rows": 3}}, {"id": "number_tf4au8ex8<PERSON>ev", "type": "number", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "number_tf4au8ex8<PERSON>ev", "customDataBind": true, "label": "计数器", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "placeholder": "请输入", "defaultValue": "", "max": null, "min": null, "step": 1, "precision": null}}, {"id": "radio_pfmouubryzghm", "type": "radio", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "radio_pfmouubryzghm", "customDataBind": true, "label": "单选框组", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "showLabel": false, "dataSourceType": "options", "options": [{"label": "Option 1", "value": "Option 1"}, {"label": "Option 2", "value": "Option 2"}, {"label": "Option 3", "value": "Option 3"}], "dictionaryType": "", "dataSourceFn": "", "defaultValue": "", "direction": "vertical"}}, {"id": "checkbox_05uosprzbdz1r", "type": "checkbox", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "checkbox_05uosprzbdz1r", "customDataBind": true, "label": "多选框组", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "showLabel": false, "dataSourceType": "options", "options": [{"label": "Option 1", "value": "Option 1"}, {"label": "Option 2", "value": "Option 2"}, {"label": "Option 3", "value": "Option 3"}], "dictionaryType": "", "dataSourceFn": "", "defaultValue": [], "direction": "vertical", "multiple": true}}, {"id": "select_uh554oxdvg9b9", "type": "select", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "select_uh554oxdvg9b9", "customDataBind": true, "label": "下拉选择", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "showLabel": false, "dataSourceType": "options", "options": [{"label": "Option 1", "value": "Option 1"}, {"label": "Option 2", "value": "Option 2"}, {"label": "Option 3", "value": "Option 3"}], "dictionaryType": "", "dataSourceFn": "", "placeholder": "请选择", "defaultValue": "", "multiple": false, "filterable": false}}, {"id": "date_tg725chbbd68w", "type": "date", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "date_tg725chbbd68w", "customDataBind": true, "label": "日期选择器", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "defaultValue": "", "placeholder": "请选择", "timestamp": false, "format": "YYYY-MM-DD HH:mm:ss", "range": [null, null], "type": "date", "inputReadOnly": true}}, {"id": "dateRange_u1jstiwiu1rke", "type": "date<PERSON><PERSON><PERSON>", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "dateRange_u1jstiwiu1rke", "customDataBind": true, "label": "日期区间选择器", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "defaultValue": [null, null], "startPlaceholder": "请选择开始时间", "endPlaceholder": "请选择结束时间", "timestamp": false, "format": "YYYY-MM-DD HH:mm:ss", "range": [null, null], "type": "date", "inputReadOnly": true}}, {"id": "rate_8qdgy2zqnpbzl", "type": "rate", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "rate_8qdgy2zqnpbzl", "customDataBind": true, "label": "评分", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "defaultValue": 0, "count": 5, "allowHalf": false, "showScore": false}}, {"id": "color_nd9je1tmdlwl1", "type": "color", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "color_nd9je1tmdlwl1", "customDataBind": true, "label": "颜色选择器", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "defaultValue": "rgba(255, 0, 0, 1)", "enableAlpha": true, "showText": false}}, {"id": "switch_y1e35bx2dxd14", "type": "switch", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "switch_y1e35bx2dxd14", "customDataBind": true, "label": "开关", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "defaultValue": false}}, {"id": "slider_psxa3cpwa6i50", "type": "slider", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "slider_psxa3cpwa6i50", "customDataBind": true, "label": "滑块", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "defaultValue": 0, "max": 100, "min": 0, "step": 1}}, {"id": "button_6pny05hkbgjv4", "type": "button", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "button_6pny05hkbgjv4", "customDataBind": true, "label": "按钮", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "defaultValue": "按钮", "type": "primary", "size": "default", "shape": "default", "ghost": false, "danger": false, "block": false}}, {"id": "text_b49f9ufketyya", "type": "text", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "text_b49f9ufketyya", "customDataBind": true, "label": "文字", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "defaultValue": "This is a text", "fontSize": 16, "color": "#000", "textAlign": "left", "lineHeight": "16px", "bold": false, "em": false, "underline": false, "del": false}}, {"id": "html_70d4a52qumoor", "type": "html", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "html_70d4a52qumoor", "customDataBind": true, "label": "HTML", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "defaultValue": "<b style=\"color: red;\">\n  This is a HTML5\n</b>"}}, {"id": "file_ppmzddx1x143a", "type": "file", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "file_ppmzddx1x143a", "customDataBind": true, "label": "文件", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "defaultValue": [], "accept": "", "action": "https://www.mocky.io/v2/5cc8019d300000980a055e76", "multiple": false, "canRemove": true, "canDownload": true, "maxCount": 9, "headers": [], "tip": "", "useCos": false, "domain": "", "tokenFunc": ""}}, {"id": "image_ku76lmcxxvjyz", "type": "image", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "image_ku76lmcxxvjyz", "customDataBind": true, "label": "图片", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "defaultValue": [], "accept": ".png,.jpeg,.gif,.webp,.jpg,.svg", "action": "https://www.mocky.io/v2/5cc8019d300000980a055e76", "multiple": false, "canRemove": true, "canDownload": true, "maxCount": 9, "headers": [], "tip": "", "useCos": false, "domain": "", "tokenFunc": "", "width": "100px", "height": "100px"}}, {"id": "editor_dkvhjkyvkuimx", "type": "editor", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "editor_dkvhjkyvkuimx", "customDataBind": true, "label": "编辑器", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "defaultValue": "", "height": "150px"}}, {"id": "cascader_3llrqozir2u2h", "type": "cascader", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "cascader_3llrqozir2u2h", "customDataBind": true, "label": "级联选择", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "placeholder": "请选择", "dataSourceType": "json", "dataSourceFn": "", "jsonOptions": "[]", "options": [], "defaultValue": "", "multiple": false, "filterable": false}}, {"id": "selectTree_qzicict21l4o0", "type": "selectTree", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "selectTree_qzicict21l4o0", "customDataBind": true, "label": "下拉选择树", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "placeholder": "请选择", "dataSourceType": "json", "dataSourceFn": "", "jsonOptions": "[]", "options": [], "defaultValue": null, "multiple": false, "filterable": false}}, {"id": "office_qsezossnppc8r", "type": "office", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "office_qsezossnppc8r", "customDataBind": true, "label": "机构", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "placeholder": "请选择", "defaultValue": null, "multiple": false, "filterable": false}}, {"id": "dict_u3ilc6sa0d0e4", "type": "dict", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "dict_u3ilc6sa0d0e4", "customDataBind": true, "label": "字典", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "placeholder": "请选择", "defaultValue": null, "multiple": false, "filterable": false, "dictionaryType": "", "options": []}}, {"id": "row_k3ui53lojhmsn", "type": "row", "props": {"visible": true, "classNames": [], "events": [], "flex": true, "gutter": 24, "align": "top", "justify": "start", "disabled": false}, "children": [{"id": "col_fzlexdkjpceom", "type": "col", "props": {"visible": true, "classNames": [], "events": [], "flex": "1", "responsive": false, "span": 24, "offset": 0, "push": 0, "pull": 0, "xs": 24, "sm": 24, "md": 24, "lg": 24, "xl": 24}, "children": []}]}, {"id": "tab_40muquzv1pbw3", "type": "tab", "props": {"visible": true, "classNames": [], "events": [], "type": "line", "tabPosition": "top", "defaultActiveKey": "tab-pane_b1eeme19cpch5"}, "children": [{"id": "tab-pane_b1eeme19cpch5", "type": "tab-pane", "props": {"visible": true, "classNames": [], "events": [], "label": "Tab 1", "disabled": false}, "children": []}]}, {"id": "collapse_wrohxmj8tbn3j", "type": "collapse", "props": {"visible": true, "classNames": [], "events": [], "defaultActiveKey": ["collapse-pane_w8yc3trgpodov"], "accordion": true, "bordered": true, "ghost": false, "expandIconPosition": "start"}, "children": [{"id": "collapse-pane_w8yc3trgpodov", "type": "collapse-pane", "props": {"visible": true, "classNames": [], "events": [], "label": "collapse pane", "disabled": false}, "children": []}]}, {"id": "table_kmxecw7g71vvk", "type": "table", "props": {"visible": true, "classNames": [], "events": [], "borderColor": "#000", "borderWidth": 1, "disabled": false}, "children": [{"id": "list-row_l6jz3jozv1r1v", "type": "list-row", "props": {"visible": true, "classNames": [], "events": []}, "children": [{"id": "table-cell_8s13t1hfiwe4m", "type": "table-cell", "props": {"visible": true, "classNames": [], "events": [], "colSpan": 1, "rowSpan": 1}, "children": []}]}]}, {"id": "divider_tyjwd7v1y3jyl", "type": "divider", "props": {"visible": true, "classNames": [], "events": [], "label": "分割线", "orientation": "center", "dashed": false, "plain": false}, "children": []}, {"id": "subtable_muv07a5w6flti", "type": "subtable", "props": {"visible": true, "classNames": [], "events": [], "dataBind": "subtable_muv07a5w6flti", "customDataBind": true, "label": "明细子表", "labelHidden": false, "labelCustomWidth": false, "useLabelWidth": true, "labelWidth": "100px", "labelSpan": 2, "labelAlign": "left", "readonly": false, "required": false, "disabled": false, "allowClear": false, "validatorFunc": "", "watches": [], "validateMode": "submit", "visibleIndexColumn": true, "usePagination": true, "canRemove": true, "canAdd": true, "summary": false}, "children": []}]}